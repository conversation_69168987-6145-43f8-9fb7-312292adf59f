#!/usr/bin/env python3
"""
Test script to demonstrate the topk_except_top1 function issue and verify the fix.
"""

import torch
import torch.nn.functional as F

def topk(x: torch.Tensor, k: int):
    """Standard topk function"""
    return x.topk(k, dim=-1, sorted=False)

def topk_except_top1(x: torch.Tensor, k: int):
    """
    Function to select the top-k but exclude the top-1.
    Instead of select top [1 -> k], we select top [2 -> (k+1)]
    """
    sel_val, sel_index = x.topk(k + 1, dim=-1, sorted=True)
    return sel_val[:, :, 1:], sel_index[:, :, 1:]

def demonstrate_issue():
    """Demonstrate the issue with the original buggy code"""
    print("=== Demonstrating the topk_except_top1 issue ===")
    
    # Create sample data
    torch.manual_seed(42)
    bsz, seq_len, n_experts = 2, 3, 8
    n_heads = 2
    
    # Create some sample selection scores
    sel2 = torch.randn(bsz, seq_len, n_experts)
    print(f"Input tensor shape: {sel2.shape}")
    print(f"Input tensor:\n{sel2}")
    print()
    
    # Original buggy behavior (calling both functions)
    print("=== BUGGY BEHAVIOR (calling both functions) ===")
    sel_val_1, sel_index_1 = topk(sel2, n_heads)
    print(f"After topk: values shape={sel_val_1.shape}, indices shape={sel_index_1.shape}")
    print(f"topk values:\n{sel_val_1}")
    print(f"topk indices:\n{sel_index_1}")
    print()
    
    # This overwrites the previous results (the bug!)
    sel_val_2, sel_index_2 = topk_except_top1(sel2, n_heads)
    print(f"After topk_except_top1: values shape={sel_val_2.shape}, indices shape={sel_index_2.shape}")
    print(f"topk_except_top1 values:\n{sel_val_2}")
    print(f"topk_except_top1 indices:\n{sel_index_2}")
    print()
    
    # Show the difference
    print("=== COMPARISON ===")
    print("Are the results the same?")
    print(f"Values equal: {torch.allclose(sel_val_1, sel_val_2)}")
    print(f"Indices equal: {torch.equal(sel_index_1, sel_index_2)}")
    print()
    
    # Show what each function is supposed to do
    print("=== WHAT EACH FUNCTION SHOULD DO ===")
    
    # Get the actual top-k+1 to see the difference
    all_vals, all_indices = sel2.topk(n_heads + 1, dim=-1, sorted=True)
    print(f"Top-{n_heads+1} values:\n{all_vals}")
    print(f"Top-{n_heads+1} indices:\n{all_indices}")
    print()
    
    print("topk() should return the top-k (including top-1):")
    print(f"Expected: indices from positions 0 to {n_heads-1}")
    print(f"Actual topk indices:\n{sel_index_1}")
    print()
    
    print("topk_except_top1() should return top-k excluding top-1:")
    print(f"Expected: indices from positions 1 to {n_heads}")
    print(f"Actual topk_except_top1 indices:\n{sel_index_2}")
    print()
    
    # Verify topk_except_top1 is working correctly
    expected_indices = all_indices[:, :, 1:n_heads+1]
    expected_values = all_vals[:, :, 1:n_heads+1]
    
    print("Verification of topk_except_top1:")
    print(f"Expected indices (positions 1 to {n_heads}):\n{expected_indices}")
    print(f"Actual topk_except_top1 indices:\n{sel_index_2}")
    print(f"Indices match: {torch.equal(expected_indices, sel_index_2)}")
    print(f"Values match: {torch.allclose(expected_values, sel_val_2)}")

if __name__ == "__main__":
    demonstrate_issue()
